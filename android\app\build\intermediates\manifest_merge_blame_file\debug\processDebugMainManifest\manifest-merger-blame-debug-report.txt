1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.legal2025.yamy"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:3:5-67
15-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:4:5-81
16-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:4:22-78
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:5:5-80
17-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:5:22-77
18    <uses-permission
18-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:6:5-7:51
19        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
19-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:6:22-79
20        android:minSdkVersion="30" /> <!-- أذونات إضافية للتحميل -->
20-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:7:22-48
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:10:5-79
21-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:10:22-76
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:11:5-68
22-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:11:22-65
23    <!--
24 Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:50:5-55:15
31        <intent>
31-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:51:9-54:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:52:13-72
32-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:52:21-70
33
34            <data android:mimeType="text/plain" />
34-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:53:13-50
34-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:53:19-48
35        </intent>
36        <intent>
36-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
37            <action android:name="android.intent.action.GET_CONTENT" />
37-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
37-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
38
39            <data android:mimeType="*/*" />
39-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:53:13-50
39-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:53:19-48
40        </intent>
41    </queries> <!-- Permissions options for the `notification` group -->
42    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
42-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
42-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
43    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
43-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
43-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
44    <uses-permission android:name="android.permission.VIBRATE" />
44-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
44-->[:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
45    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
45-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe748e000d283452c05d1c261cc6f47c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
45-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe748e000d283452c05d1c261cc6f47c\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13f2593a09a7e676e6a4e2c9d70ef7ac\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13f2593a09a7e676e6a4e2c9d70ef7ac\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13f2593a09a7e676e6a4e2c9d70ef7ac\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13f2593a09a7e676e6a4e2c9d70ef7ac\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13f2593a09a7e676e6a4e2c9d70ef7ac\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52
53    <application
54        android:name="android.app.Application"
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13f2593a09a7e676e6a4e2c9d70ef7ac\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:icon="@mipmap/ic_launcher"
59        android:label="Legal2025" >
60        <activity
61            android:name="com.legal2025.yamy.MainActivity"
62            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
63            android:exported="true"
64            android:hardwareAccelerated="true"
65            android:launchMode="singleTop"
66            android:taskAffinity=""
67            android:theme="@style/LaunchTheme"
68            android:windowSoftInputMode="adjustResize" >
69
70            <!--
71                 Specifies an Android theme to apply to this Activity as soon as
72                 the Android process has started. This theme is visible to the user
73                 while the Flutter UI initializes. After that, this theme continues
74                 to determine the Window background behind the Flutter UI.
75            -->
76            <meta-data
77                android:name="io.flutter.embedding.android.NormalTheme"
78                android:resource="@style/NormalTheme" />
79
80            <intent-filter>
81                <action android:name="android.intent.action.MAIN" />
82
83                <category android:name="android.intent.category.LAUNCHER" />
84            </intent-filter>
85        </activity>
86        <!--
87             Don't delete the meta-data below.
88             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
89        -->
90        <meta-data
91            android:name="flutterEmbedding"
92            android:value="2" />
93
94        <service
94-->[:cloud_functions] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_functions-5.6.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
95            android:name="com.google.firebase.components.ComponentDiscoveryService"
95-->[:cloud_functions] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_functions-5.6.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
96            android:directBootAware="true"
96-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
97            android:exported="false" >
97-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
98            <meta-data
98-->[:cloud_functions] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_functions-5.6.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
99                android:name="com.google.firebase.components:io.flutter.plugins.firebase.functions.FlutterFirebaseAppRegistrar"
99-->[:cloud_functions] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_functions-5.6.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-128
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[:cloud_functions] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_functions-5.6.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
101            <meta-data
101-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
102                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
102-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
104            <meta-data
104-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
105                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
105-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
107            <meta-data
107-->[:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
108                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
108-->[:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
110            <meta-data
110-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
111                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
111-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
113            <meta-data
113-->[:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
114                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
114-->[:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
116            <meta-data
116-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
117                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
117-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
119            <meta-data
119-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
120                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
120-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
122            <meta-data
122-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204b6deab33d504dcfa4ed340803e35d\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
123                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
123-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204b6deab33d504dcfa4ed340803e35d\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204b6deab33d504dcfa4ed340803e35d\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
125            <meta-data
125-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204b6deab33d504dcfa4ed340803e35d\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
126                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
126-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204b6deab33d504dcfa4ed340803e35d\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204b6deab33d504dcfa4ed340803e35d\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
128            <meta-data
128-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66171060c6a542efc3b5699c95cf1572\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
129                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
129-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66171060c6a542efc3b5699c95cf1572\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66171060c6a542efc3b5699c95cf1572\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
131            <meta-data
131-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66171060c6a542efc3b5699c95cf1572\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
132                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
132-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66171060c6a542efc3b5699c95cf1572\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66171060c6a542efc3b5699c95cf1572\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
134            <meta-data
134-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5a3d108dd17788c661450843e483dd1\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:15:13-17:85
135                android:name="com.google.firebase.components:com.google.firebase.functions.FirebaseFunctionsKtxRegistrar"
135-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5a3d108dd17788c661450843e483dd1\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:16:17-122
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5a3d108dd17788c661450843e483dd1\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:17:17-82
137            <meta-data
137-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5a3d108dd17788c661450843e483dd1\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:18:13-20:85
138                android:name="com.google.firebase.components:com.google.firebase.functions.FunctionsRegistrar"
138-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5a3d108dd17788c661450843e483dd1\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:19:17-111
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-functions:21.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5a3d108dd17788c661450843e483dd1\transformed\jetified-firebase-functions-21.2.1\AndroidManifest.xml:20:17-82
140            <meta-data
140-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
141                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
141-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
143            <meta-data
143-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
144                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
144-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
146            <meta-data
146-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a61b3763db1e2379e33d1eddcaf28a2b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
147                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
147-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a61b3763db1e2379e33d1eddcaf28a2b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a61b3763db1e2379e33d1eddcaf28a2b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
149            <meta-data
149-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a61b3763db1e2379e33d1eddcaf28a2b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
150                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
150-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a61b3763db1e2379e33d1eddcaf28a2b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a61b3763db1e2379e33d1eddcaf28a2b\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
152            <meta-data
152-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6dd2b5532b44518c8b8fcfbd013797d1\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
153                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
153-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6dd2b5532b44518c8b8fcfbd013797d1\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6dd2b5532b44518c8b8fcfbd013797d1\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
155            <meta-data
155-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd30e4891b8951c35c82502798666d91\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
156                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
156-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd30e4891b8951c35c82502798666d91\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd30e4891b8951c35c82502798666d91\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
158            <meta-data
158-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd30e4891b8951c35c82502798666d91\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
159                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
159-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd30e4891b8951c35c82502798666d91\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd30e4891b8951c35c82502798666d91\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
161            <meta-data
161-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b2a8274f2ae8982efa3c681bef89ee0\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
162                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
162-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b2a8274f2ae8982efa3c681bef89ee0\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b2a8274f2ae8982efa3c681bef89ee0\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
164            <meta-data
164-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b2a8274f2ae8982efa3c681bef89ee0\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
165                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
165-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b2a8274f2ae8982efa3c681bef89ee0\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b2a8274f2ae8982efa3c681bef89ee0\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
167            <meta-data
167-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c023c9c3c7e124eba3032503fa20fc2f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
168                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
168-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c023c9c3c7e124eba3032503fa20fc2f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c023c9c3c7e124eba3032503fa20fc2f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
170            <meta-data
170-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
171                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
171-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
173            <meta-data
173-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e77c215557459ae56bf1810032d8a567\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
174                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
174-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e77c215557459ae56bf1810032d8a567\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e77c215557459ae56bf1810032d8a567\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
176        </service>
177        <service
177-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
178            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
178-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
179            android:exported="false"
179-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
180            android:permission="android.permission.BIND_JOB_SERVICE" />
180-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
181        <service
181-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
182            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
182-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
183            android:exported="false" >
183-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
184            <intent-filter>
184-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
185                <action android:name="com.google.firebase.MESSAGING_EVENT" />
185-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
185-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
186            </intent-filter>
187        </service>
188
189        <receiver
189-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
190            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
190-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
191            android:exported="true"
191-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
192            android:permission="com.google.android.c2dm.permission.SEND" >
192-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
193            <intent-filter>
193-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
194                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
194-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
194-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
195            </intent-filter>
196        </receiver>
197
198        <provider
198-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
199            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
199-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
200            android:authorities="com.legal2025.yamy.flutterfirebasemessaginginitprovider"
200-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
201            android:exported="false"
201-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
202            android:initOrder="99" />
202-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
203
204        <activity
204-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
205            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
205-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
206            android:excludeFromRecents="true"
206-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
207            android:exported="true"
207-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
208            android:launchMode="singleTask"
208-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
209            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
209-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
210            <intent-filter>
210-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
211                <action android:name="android.intent.action.VIEW" />
211-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
211-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
212
213                <category android:name="android.intent.category.DEFAULT" />
213-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
213-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
214                <category android:name="android.intent.category.BROWSABLE" />
214-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
214-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
215
216                <data
216-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:53:13-50
217                    android:host="firebase.auth"
218                    android:path="/"
219                    android:scheme="genericidp" />
220            </intent-filter>
221        </activity>
222        <activity
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
223            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
223-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
224            android:excludeFromRecents="true"
224-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
225            android:exported="true"
225-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
226            android:launchMode="singleTask"
226-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
227            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
227-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
228            <intent-filter>
228-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
229                <action android:name="android.intent.action.VIEW" />
229-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
229-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
230
231                <category android:name="android.intent.category.DEFAULT" />
231-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
231-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
232                <category android:name="android.intent.category.BROWSABLE" />
232-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
232-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
233
234                <data
234-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:53:13-50
235                    android:host="firebase.auth"
236                    android:path="/"
237                    android:scheme="recaptcha" />
238            </intent-filter>
239        </activity>
240
241        <receiver
241-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
242            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
242-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
243            android:exported="true"
243-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
244            android:permission="com.google.android.c2dm.permission.SEND" >
244-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
245            <intent-filter>
245-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
246                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
246-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
246-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
247            </intent-filter>
248
249            <meta-data
249-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
250                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
250-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
251                android:value="true" />
251-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
252        </receiver>
253        <!--
254             FirebaseMessagingService performs security checks at runtime,
255             but set to not exported to explicitly avoid allowing another app to call it.
256        -->
257        <service
257-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
258            android:name="com.google.firebase.messaging.FirebaseMessagingService"
258-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
259            android:directBootAware="true"
259-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
260            android:exported="false" >
260-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
261            <intent-filter android:priority="-500" >
261-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
262                <action android:name="com.google.firebase.MESSAGING_EVENT" />
262-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
262-->[:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
263            </intent-filter>
264        </service>
265
266        <provider
266-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
267            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
267-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
268            android:authorities="com.legal2025.yamy.flutter.image_provider"
268-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
269            android:exported="false"
269-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
270            android:grantUriPermissions="true" >
270-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
271            <meta-data
271-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
272                android:name="android.support.FILE_PROVIDER_PATHS"
272-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
273                android:resource="@xml/flutter_image_picker_file_paths" />
273-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
274        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
275        <service
275-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
276            android:name="com.google.android.gms.metadata.ModuleDependencies"
276-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
277            android:enabled="false"
277-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
278            android:exported="false" >
278-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
279            <intent-filter>
279-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
280                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
280-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
280-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
281            </intent-filter>
282
283            <meta-data
283-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
284                android:name="photopicker_activity:0:required"
284-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
285                android:value="" />
285-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
286        </service>
287
288        <activity
288-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
289            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
289-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
290            android:exported="false"
290-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
291            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
291-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
292
293        <service
293-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
294            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
294-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
295            android:enabled="true"
295-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
296            android:exported="false" >
296-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
297            <meta-data
297-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
298                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
298-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
299                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
299-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
300        </service>
301
302        <activity
302-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
303            android:name="androidx.credentials.playservices.HiddenActivity"
303-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
304            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
304-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
305            android:enabled="true"
305-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
306            android:exported="false"
306-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
307            android:fitsSystemWindows="true"
307-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
308            android:theme="@style/Theme.Hidden" >
308-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
309        </activity>
310        <activity
310-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
311            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
311-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
312            android:excludeFromRecents="true"
312-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
313            android:exported="false"
313-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
314            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
314-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
315        <!--
316            Service handling Google Sign-In user revocation. For apps that do not integrate with
317            Google Sign-In, this service will never be started.
318        -->
319        <service
319-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
320            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
320-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
321            android:exported="true"
321-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
322            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
322-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
323            android:visibleToInstantApps="true" />
323-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
324
325        <provider
325-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
326            android:name="com.google.firebase.provider.FirebaseInitProvider"
326-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
327            android:authorities="com.legal2025.yamy.firebaseinitprovider"
327-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
328            android:directBootAware="true"
328-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
329            android:exported="false"
329-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
330            android:initOrder="100" />
330-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
331
332        <activity
332-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305e40512bf13f0ccd5d12e0c8791b8d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
333            android:name="com.google.android.gms.common.api.GoogleApiActivity"
333-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305e40512bf13f0ccd5d12e0c8791b8d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
334            android:exported="false"
334-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305e40512bf13f0ccd5d12e0c8791b8d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
335            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
335-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305e40512bf13f0ccd5d12e0c8791b8d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
336
337        <provider
337-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb8761ee32dcc9accca793e710c9cf07\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
338            android:name="androidx.startup.InitializationProvider"
338-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb8761ee32dcc9accca793e710c9cf07\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
339            android:authorities="com.legal2025.yamy.androidx-startup"
339-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb8761ee32dcc9accca793e710c9cf07\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
340            android:exported="false" >
340-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb8761ee32dcc9accca793e710c9cf07\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
341            <meta-data
341-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb8761ee32dcc9accca793e710c9cf07\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
342                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
342-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb8761ee32dcc9accca793e710c9cf07\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
343                android:value="androidx.startup" />
343-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb8761ee32dcc9accca793e710c9cf07\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
344            <meta-data
344-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
345                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
346                android:value="androidx.startup" />
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
347        </provider>
348
349        <uses-library
349-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44a6db5ec6cee4d10c55825c1ba848aa\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
350            android:name="androidx.window.extensions"
350-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44a6db5ec6cee4d10c55825c1ba848aa\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
351            android:required="false" />
351-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44a6db5ec6cee4d10c55825c1ba848aa\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
352        <uses-library
352-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44a6db5ec6cee4d10c55825c1ba848aa\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
353            android:name="androidx.window.sidecar"
353-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44a6db5ec6cee4d10c55825c1ba848aa\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
354            android:required="false" />
354-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44a6db5ec6cee4d10c55825c1ba848aa\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
355
356        <meta-data
356-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5813dff2fc04f128ab917c6cfbab969f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
357            android:name="com.google.android.gms.version"
357-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5813dff2fc04f128ab917c6cfbab969f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
358            android:value="@integer/google_play_services_version" />
358-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5813dff2fc04f128ab917c6cfbab969f\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
359
360        <receiver
360-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
361            android:name="androidx.profileinstaller.ProfileInstallReceiver"
361-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
362            android:directBootAware="false"
362-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
363            android:enabled="true"
363-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
364            android:exported="true"
364-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
365            android:permission="android.permission.DUMP" >
365-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
366            <intent-filter>
366-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
367                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
367-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
367-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
368            </intent-filter>
369            <intent-filter>
369-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
370                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
370-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
370-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
371            </intent-filter>
372            <intent-filter>
372-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
373                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
373-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
373-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
374            </intent-filter>
375            <intent-filter>
375-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
376                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
376-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
376-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
377            </intent-filter>
378        </receiver>
379
380        <service
380-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2991070276e78974c29daf5c7455c9b4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
381            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
381-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2991070276e78974c29daf5c7455c9b4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
382            android:exported="false" >
382-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2991070276e78974c29daf5c7455c9b4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
383            <meta-data
383-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2991070276e78974c29daf5c7455c9b4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
384                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
384-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2991070276e78974c29daf5c7455c9b4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
385                android:value="cct" />
385-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2991070276e78974c29daf5c7455c9b4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
386        </service>
387        <service
387-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0af0d6a33b939b4edcb4d7de9e1fdd30\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
388            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
388-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0af0d6a33b939b4edcb4d7de9e1fdd30\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
389            android:exported="false"
389-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0af0d6a33b939b4edcb4d7de9e1fdd30\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
390            android:permission="android.permission.BIND_JOB_SERVICE" >
390-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0af0d6a33b939b4edcb4d7de9e1fdd30\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
391        </service>
392
393        <receiver
393-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0af0d6a33b939b4edcb4d7de9e1fdd30\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
394            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
394-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0af0d6a33b939b4edcb4d7de9e1fdd30\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
395            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
395-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0af0d6a33b939b4edcb4d7de9e1fdd30\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
396        <activity
396-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19501761fecbdb52c8e982479daffff6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
397            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
397-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19501761fecbdb52c8e982479daffff6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
398            android:exported="false"
398-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19501761fecbdb52c8e982479daffff6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
399            android:stateNotNeeded="true"
399-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19501761fecbdb52c8e982479daffff6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
400            android:theme="@style/Theme.PlayCore.Transparent" />
400-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19501761fecbdb52c8e982479daffff6\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
401    </application>
402
403</manifest>
