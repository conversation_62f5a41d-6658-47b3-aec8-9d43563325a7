{"logs": [{"outputFile": "com.legal2025.yamy.app-mergeDebugResources-69:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1214412e3b6f657b0a3026c46d581630\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2832,2944", "endColumns": "111,113", "endOffsets": "2939,3053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76de88675158c32d011d90413b6fc543\\transformed\\preference-1.2.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,348,488,657,737", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "172,260,343,483,652,732,808"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6140,6316,6716,6799,7121,7290,7370", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "6207,6399,6794,6934,7285,7365,7441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e53585da63edf0e2fa4a6374876313c7\\transformed\\appcompat-1.1.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,881,973,1066,1162,1263,1371,1471,1575,1673,1771,1868,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,75,91,92,95,100,107,99,103,97,97,96,80,110,101,97,106,102,103,155,101,80", "endOffsets": "205,303,415,501,607,722,800,876,968,1061,1157,1258,1366,1466,1570,1668,1766,1863,1944,2055,2157,2255,2362,2465,2569,2725,2827,2908"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,881,973,1066,1162,1263,1371,1471,1575,1673,1771,1868,1949,2060,2162,2260,2367,2470,2574,2730,6939", "endColumns": "104,97,111,85,105,114,77,75,91,92,95,100,107,99,103,97,97,96,80,110,101,97,106,102,103,155,101,80", "endOffsets": "205,303,415,501,607,722,800,876,968,1061,1157,1258,1366,1466,1570,1668,1766,1863,1944,2055,2157,2255,2362,2465,2569,2725,2827,7015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f2593a09a7e676e6a4e2c9d70ef7ac\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3058,3156,3258,3358,3458,3566,3671,7020", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3151,3253,3353,3453,3561,3666,3784,7116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\305e40512bf13f0ccd5d12e0c8791b8d\\transformed\\jetified-play-services-base-18.1.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3789,3898,4062,4190,4302,4480,4611,4732,4996,5176,5288,5457,5588,5750,5926,5997,6060", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "3893,4057,4185,4297,4475,4606,4727,4846,5171,5283,5452,5583,5745,5921,5992,6055,6135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5813dff2fc04f128ab917c6cfbab969f\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4851", "endColumns": "144", "endOffsets": "4991"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7be1c0bb4075e2006ba23d0329c62fdb\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6212,6404,6505,6616", "endColumns": "103,100,110,99", "endOffsets": "6311,6500,6611,6711"}}]}]}