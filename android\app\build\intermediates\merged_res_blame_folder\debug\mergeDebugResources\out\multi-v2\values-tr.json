{"logs": [{"outputFile": "com.legal2025.yamy.app-mergeDebugResources-69:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5813dff2fc04f128ab917c6cfbab969f\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4756", "endColumns": "146", "endOffsets": "4898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1214412e3b6f657b0a3026c46d581630\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,120", "endOffsets": "159,280"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2797,2906", "endColumns": "108,120", "endOffsets": "2901,3022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7be1c0bb4075e2006ba23d0329c62fdb\\transformed\\browser-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6062,6249,6355,6462", "endColumns": "99,105,106,105", "endOffsets": "6157,6350,6457,6563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f2593a09a7e676e6a4e2c9d70ef7ac\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3027,3124,3226,3324,3421,3523,3629,6857", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3119,3221,3319,3416,3518,3624,3735,6953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\305e40512bf13f0ccd5d12e0c8791b8d\\transformed\\jetified-play-services-base-18.1.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3740,3850,4005,4141,4246,4393,4523,4650,4903,5075,5182,5339,5473,5618,5785,5847,5911", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "3845,4000,4136,4241,4388,4518,4645,4751,5070,5177,5334,5468,5613,5780,5842,5906,5986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e53585da63edf0e2fa4a6374876313c7\\transformed\\appcompat-1.1.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,897,988,1080,1172,1266,1367,1460,1562,1657,1748,1839,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,75,90,91,91,93,100,92,101,94,90,90,77,106,103,95,106,102,108,155,97,78", "endOffsets": "214,313,425,510,616,736,816,892,983,1075,1167,1261,1362,1455,1557,1652,1743,1834,1912,2019,2123,2219,2326,2429,2538,2694,2792,2871"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,897,988,1080,1172,1266,1367,1460,1562,1657,1748,1839,1917,2024,2128,2224,2331,2434,2543,2699,6778", "endColumns": "113,98,111,84,105,119,79,75,90,91,91,93,100,92,101,94,90,90,77,106,103,95,106,102,108,155,97,78", "endOffsets": "214,313,425,510,616,736,816,892,983,1075,1167,1261,1362,1455,1557,1652,1743,1834,1912,2019,2123,2219,2326,2429,2538,2694,2792,6852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\76de88675158c32d011d90413b6fc543\\transformed\\preference-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,341,473,642,725", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "171,258,336,468,637,720,798"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5991,6162,6568,6646,6958,7127,7210", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "6057,6244,6641,6773,7122,7205,7283"}}]}]}