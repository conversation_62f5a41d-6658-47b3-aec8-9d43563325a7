<libraries>
  <library
      name="__local_aars__:D:\20223\2025\legl92025\android\app\build\intermediates\flutter\release\libs.jar:unspecified@jar"
      jars="D:\20223\2025\legl92025\android\app\build\intermediates\flutter\release\libs.jar"
      resolved="__local_aars__:D:\20223\2025\legl92025\android\app\build\intermediates\flutter\release\libs.jar:unspecified"/>
  <library
      name=":@@:cloud_functions::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d302d030046eccdb786f957a6d9c32d4\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d302d030046eccdb786f957a6d9c32d4\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebase.cloudfunctions:cloud_functions:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_functions-5.6.1\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d302d030046eccdb786f957a6d9c32d4\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:shared_preferences_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d2af70b425a98477f9ea23dbfff8a5c\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d2af70b425a98477f9ea23dbfff8a5c\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.sharedpreferences:shared_preferences_android:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d2af70b425a98477f9ea23dbfff8a5c\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name=":@@:cloud_firestore::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a404c927484d3deacca84d90677bfbc\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a404c927484d3deacca84d90677bfbc\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebase.cloudfirestore:cloud_firestore:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a404c927484d3deacca84d90677bfbc\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:connectivity_plus::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e7c223de84788c9c10fcb758d80aa9b\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e7c223de84788c9c10fcb758d80aa9b\transformed\out\jars\libs\R.jar"
      resolved="dev.fluttercommunity.plus.connectivity:connectivity_plus:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e7c223de84788c9c10fcb758d80aa9b\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:device_info_plus::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f16117c6ec344a328c3b270c6ab13d6a\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f16117c6ec344a328c3b270c6ab13d6a\transformed\out\jars\libs\R.jar"
      resolved="dev.fluttercommunity.plus.device_info:device_info_plus:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\device_info_plus-11.5.0\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f16117c6ec344a328c3b270c6ab13d6a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:file_picker::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\107fb7b6bd7114a9595c7a3898673c0f\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\107fb7b6bd7114a9595c7a3898673c0f\transformed\out\jars\libs\R.jar"
      resolved="com.mr.flutter.plugin.filepicker:file_picker:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\107fb7b6bd7114a9595c7a3898673c0f\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_auth::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e459be97514d61f90496a024654b11\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e459be97514d61f90496a024654b11\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebase.auth:firebase_auth:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88e459be97514d61f90496a024654b11\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_database::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad316ed2f59121cd09d44381822853a\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad316ed2f59121cd09d44381822853a\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebase.database:firebase_database:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ad316ed2f59121cd09d44381822853a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_messaging::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\267d6227843a98210b6e7495bf917235\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\267d6227843a98210b6e7495bf917235\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebasemessaging:firebase_messaging:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\267d6227843a98210b6e7495bf917235\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_storage::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6111b570ea52df37bd7eabef5f6e56ae\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6111b570ea52df37bd7eabef5f6e56ae\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebase.storage:firebase_storage:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6111b570ea52df37bd7eabef5f6e56ae\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:firebase_core::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ecd4f87242a81580edf8d7b3201af6\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ecd4f87242a81580edf8d7b3201af6\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.firebase.core:firebase_core:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ecd4f87242a81580edf8d7b3201af6\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_local_notifications::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d624b1f650c3947344002346d9381de\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d624b1f650c3947344002346d9381de\transformed\out\jars\libs\R.jar"
      resolved="com.dexterous.flutterlocalnotifications:flutter_local_notifications:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d624b1f650c3947344002346d9381de\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_pdfview::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4a82d16b34193b33d2823c47d0c2821\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4a82d16b34193b33d2823c47d0c2821\transformed\out\jars\libs\R.jar"
      resolved="io.endigo.plugins.pdfview:flutter_pdfview:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_pdfview-1.4.1+1\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4a82d16b34193b33d2823c47d0c2821\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:flutter_plugin_android_lifecycle::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc74e554f7afb39a61cd55aa3a7e6deb\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc74e554f7afb39a61cd55aa3a7e6deb\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.flutter_plugin_android_lifecycle:flutter_plugin_android_lifecycle:1.0"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc74e554f7afb39a61cd55aa3a7e6deb\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:google_sign_in_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9187cfb0f831220d9b652144ec0c2eee\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9187cfb0f831220d9b652144ec0c2eee\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.googlesignin:google_sign_in_android:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9187cfb0f831220d9b652144ec0c2eee\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:image_picker_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f7abee682e5be56cd19b4df7bc3768e\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f7abee682e5be56cd19b4df7bc3768e\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.imagepicker:image_picker_android:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f7abee682e5be56cd19b4df7bc3768e\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:package_info_plus::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1ea4688f6b37dd48c3e1ccf89c42eb7\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1ea4688f6b37dd48c3e1ccf89c42eb7\transformed\out\jars\libs\R.jar"
      resolved="dev.fluttercommunity.plus.packageinfo:package_info_plus:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\package_info_plus-8.3.0\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1ea4688f6b37dd48c3e1ccf89c42eb7\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:path_provider_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\871426ca0c8e47ddc271ab24092e1ee4\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\871426ca0c8e47ddc271ab24092e1ee4\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.pathprovider:path_provider_android:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\871426ca0c8e47ddc271ab24092e1ee4\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:permission_handler_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5b8ffd1f2d6b3718800abdddb4db2f1\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5b8ffd1f2d6b3718800abdddb4db2f1\transformed\out\jars\libs\R.jar"
      resolved="com.baseflow.permissionhandler:permission_handler_android:1.0"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\permission_handler_android-13.0.1\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5b8ffd1f2d6b3718800abdddb4db2f1\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:syncfusion_flutter_pdfviewer::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1810e54b3f2429095f3895f247b01867\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1810e54b3f2429095f3895f247b01867\transformed\out\jars\libs\R.jar"
      resolved="com.syncfusion.flutter.pdfviewer:syncfusion_flutter_pdfviewer:1.0"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\syncfusion_flutter_pdfviewer-30.1.39\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1810e54b3f2429095f3895f247b01867\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:url_launcher_android::release"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8afef2901ed99cb20679a76ebfc12e5\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8afef2901ed99cb20679a76ebfc12e5\transformed\out\jars\libs\R.jar"
      resolved="io.flutter.plugins.urllauncher:url_launcher_android:1.0-SNAPSHOT"
      partialResultsDir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8afef2901ed99cb20679a76ebfc12e5\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\flutter_embedding_release\1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa\feb8933ac1af7804d75a026eb818d0479a4b2814\flutter_embedding_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaa214e283e9fc31b460d74f33a3048c\transformed\fragment-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aaa214e283e9fc31b460d74f33a3048c\transformed\fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09af1acd0243272a2fc2b8636ba12c20\transformed\jetified-activity-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09af1acd0243272a2fc2b8636ba12c20\transformed\jetified-activity-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afb16f0ae7bd0257ae9e2162f0908263\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afb16f0ae7bd0257ae9e2162f0908263\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1bee42a6a1ac63167d59f2b6460e0e34\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1bee42a6a1ac63167d59f2b6460e0e34\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1305d72043d15b54de3db0e0de52483e\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1305d72043d15b54de3db0e0de52483e\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f93407762a4dbb60e67eeef908734bc1\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f93407762a4dbb60e67eeef908734bc1\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8056f5af9c082f198ed7a9c2da9c1447\transformed\jetified-core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8056f5af9c082f198ed7a9c2da9c1447\transformed\jetified-core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a23f07556fed317bc236996f3ccb9ff\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a23f07556fed317bc236996f3ccb9ff\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecbef45ce60e462d8567d848673a5178\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecbef45ce60e462d8567d848673a5178\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13f2593a09a7e676e6a4e2c9d70ef7ac\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13f2593a09a7e676e6a4e2c9d70ef7ac\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2904d3680b807a7b9a7de5b3adcbd1a4\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2904d3680b807a7b9a7de5b3adcbd1a4\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb8761ee32dcc9accca793e710c9cf07\transformed\jetified-lifecycle-process-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb8761ee32dcc9accca793e710c9cf07\transformed\jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.7.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44a6db5ec6cee4d10c55825c1ba848aa\transformed\jetified-window-1.2.0\jars\classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44a6db5ec6cee4d10c55825c1ba848aa\transformed\jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\855876ed97b60d463c09b1de21b87671\transformed\jetified-window-java-1.2.0\jars\classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\855876ed97b60d463c09b1de21b87671\transformed\jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e6f274a683998f7b49c94bbcbd44b88\transformed\jetified-annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e6f274a683998f7b49c94bbcbd44b88\transformed\jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f38bbe38c62130c874a7fedc0088d8db\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f38bbe38c62130c874a7fedc0088d8db\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9280e926910052951d58e4c763c2f7b3\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9280e926910052951d58e4c763c2f7b3\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.24\9928532f12c66ad816a625b3f9984f8368ca6d2b\kotlin-stdlib-1.9.24.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.24"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\armeabi_v7a_release\1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa\b6f4f69fec231389b192723a46e0e0d5b53b85db\armeabi_v7a_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\arm64_v8a_release\1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa\5adac80dc087c6b4eca5a03b665d7097d7817006\arm64_v8a_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.flutter\x86_64_release\1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa\5cb0b818f7233be6a4a6f2366198573ce461c74\x86_64_release-1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa.jar"
      resolved="io.flutter:x86_64_release:1.0.0-cf56914b326edb0ccb123ffdc60f00060bd513fa"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76aeefdcdc1717e276ef95ad21040026\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76aeefdcdc1717e276ef95ad21040026\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9043cf3763ac458d4b9b37cf9c0fa17\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9043cf3763ac458d4b9b37cf9c0fa17\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\437b884edce4a952eff8b6a8f1c34fe0\transformed\jetified-relinker-1.4.5\jars\classes.jar"
      resolved="com.getkeepsafe.relinker:relinker:1.4.5"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\437b884edce4a952eff8b6a8f1c34fe0\transformed\jetified-relinker-1.4.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth:23.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth:23.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5853f920bcb14c97320c46e905b30084\transformed\jetified-firebase-auth-23.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204b6deab33d504dcfa4ed340803e35d\transformed\jetified-firebase-database-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-database:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\204b6deab33d504dcfa4ed340803e35d\transformed\jetified-firebase-database-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-firestore:25.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66171060c6a542efc3b5699c95cf1572\transformed\jetified-firebase-firestore-25.1.4\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore:25.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66171060c6a542efc3b5699c95cf1572\transformed\jetified-firebase-firestore-25.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-functions:21.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5a3d108dd17788c661450843e483dd1\transformed\jetified-firebase-functions-21.2.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-functions:21.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5a3d108dd17788c661450843e483dd1\transformed\jetified-firebase-functions-21.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e579fafb1a43350ef4cd8b8959df0488\transformed\jetified-firebase-messaging-24.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-storage:21.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a61b3763db1e2379e33d1eddcaf28a2b\transformed\jetified-firebase-storage-21.0.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-storage:21.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a61b3763db1e2379e33d1eddcaf28a2b\transformed\jetified-firebase-storage-21.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid:21.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6dd2b5532b44518c8b8fcfbd013797d1\transformed\jetified-firebase-iid-21.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid:21.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6dd2b5532b44518c8b8fcfbd013797d1\transformed\jetified-firebase-iid-21.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd30e4891b8951c35c82502798666d91\transformed\jetified-firebase-installations-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd30e4891b8951c35c82502798666d91\transformed\jetified-firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b2a8274f2ae8982efa3c681bef89ee0\transformed\jetified-firebase-appcheck-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b2a8274f2ae8982efa3c681bef89ee0\transformed\jetified-firebase-appcheck-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c023c9c3c7e124eba3032503fa20fc2f\transformed\jetified-firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c023c9c3c7e124eba3032503fa20fc2f\transformed\jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d0a3f573332b3be097db894daafbd8d\transformed\media-1.1.0\jars\classes.jar"
      resolved="androidx.media:media:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d0a3f573332b3be097db894daafbd8d\transformed\media-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.mhiew:android-pdf-viewer:3.2.0-beta.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d86c4b0b9a6035670ca0318f4a106b\transformed\jetified-android-pdf-viewer-3.2.0-beta.3\jars\classes.jar"
      resolved="com.github.mhiew:android-pdf-viewer:3.2.0-beta.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26d86c4b0b9a6035670ca0318f4a106b\transformed\jetified-android-pdf-viewer-3.2.0-beta.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials:1.2.0-rc01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1214412e3b6f657b0a3026c46d581630\transformed\jetified-credentials-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials:1.2.0-rc01"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1214412e3b6f657b0a3026c46d581630\transformed\jetified-credentials-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.2.0-rc01"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5a70d0ba7abaf74188a334ea68a3f12\transformed\jetified-credentials-play-services-auth-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a7bd6176d5fad649af25fec6869cf8b\transformed\jetified-play-services-auth-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76de88675158c32d011d90413b6fc543\transformed\preference-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76de88675158c32d011d90413b6fc543\transformed\preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e53585da63edf0e2fa4a6374876313c7\transformed\appcompat-1.1.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e53585da63edf0e2fa4a6374876313c7\transformed\appcompat-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd4040dc86f202291b82babffa79dbbf\transformed\jetified-firebase-auth-interop-20.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd4040dc86f202291b82babffa79dbbf\transformed\jetified-firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b558d68495a53493017f0a8f85f232c\transformed\jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b44d5ba70bcfa5086ebd3aa5d57fef8\transformed\jetified-play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b44d5ba70bcfa5086ebd3aa5d57fef8\transformed\jetified-play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4148b7d4316056ab6a461c43b8596aa3\transformed\jetified-play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4148b7d4316056ab6a461c43b8596aa3\transformed\jetified-play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe748e000d283452c05d1c261cc6f47c\transformed\jetified-recaptcha-18.6.1\jars\classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe748e000d283452c05d1c261cc6f47c\transformed\jetified-recaptcha-18.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:integrity:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ba39607b04c8bc62f87396e38f6aec2\transformed\jetified-integrity-1.3.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ba39607b04c8bc62f87396e38f6aec2\transformed\jetified-integrity-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a9816f406f220bc08231b05ccfe0577\transformed\jetified-firebase-appcheck-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a9816f406f220bc08231b05ccfe0577\transformed\jetified-firebase-appcheck-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database-collection:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec9550885a1ce7dbe08ed29806df3e1f\transformed\jetified-firebase-database-collection-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-database-collection:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec9550885a1ce7dbe08ed29806df3e1f\transformed\jetified-firebase-database-collection-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96a79821779c2a27d51461a39ccd5f6\transformed\jetified-play-services-fido-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96a79821779c2a27d51461a39ccd5f6\transformed\jetified-play-services-fido-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305e40512bf13f0ccd5d12e0c8791b8d\transformed\jetified-play-services-base-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\305e40512bf13f0ccd5d12e0c8791b8d\transformed\jetified-play-services-base-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7279dd836a400e3062223f76616a3e3\transformed\jetified-firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7279dd836a400e3062223f76616a3e3\transformed\jetified-firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aabc676ad644a6d184c76d025562b58\transformed\jetified-play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aabc676ad644a6d184c76d025562b58\transformed\jetified-play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a72ed3d527ab8a7724c897cab3a931e2\transformed\jetified-play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a72ed3d527ab8a7724c897cab3a931e2\transformed\jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0350436104f0549bbfb54632372cde\transformed\recyclerview-1.0.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb0350436104f0549bbfb54632372cde\transformed\recyclerview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68a63bf7bc974931ed9be62f6a93ccec\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68a63bf7bc974931ed9be62f6a93ccec\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\197e0d709aaba7cae4b37372ec29dc70\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\197e0d709aaba7cae4b37372ec29dc70\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec2165e08b92c8ed3d38095ffd81db01\transformed\jetified-fragment-ktx-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec2165e08b92c8ed3d38095ffd81db01\transformed\jetified-fragment-ktx-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33a5c2f8a9b49c609ba5848c77e19600\transformed\jetified-activity-ktx-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33a5c2f8a9b49c609ba5848c77e19600\transformed\jetified-activity-ktx-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769d6d21da18548afee10072c48ab960\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\769d6d21da18548afee10072c48ab960\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8bfc091584678ac8c37a8cae5115d82\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8bfc091584678ac8c37a8cae5115d82\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e8e33f4cd9ce02b8a045b9a7f0acd80\transformed\jetified-lifecycle-runtime-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e8e33f4cd9ce02b8a045b9a7f0acd80\transformed\jetified-lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed37f6d88b533e6c93c6549a0938ba43\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed37f6d88b533e6c93c6549a0938ba43\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d65cdfc10ea89ffbead4ea274fefc424\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d65cdfc10ea89ffbead4ea274fefc424\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-external-protobuf\1.1.3\ddd0a2d64e3c928359c993d1291e535d5d7fc9a3\datastore-preferences-external-protobuf-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-proto\1.1.3\6d7430ed8d2b5f2b8675dad8d196ba5dd710921b\datastore-preferences-proto-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core-jvm\1.1.3\fb991f11389ccf2a5d5d4c99783ff958bc400\datastore-preferences-core-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-core-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.datastore\datastore-core-okio-jvm\1.1.3\ce08f132812044a9778547b299fd812e34dbd602\datastore-core-okio-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\269e6a5fd9b0491339a26524c8fe0a75\transformed\jetified-datastore-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\269e6a5fd9b0491339a26524c8fe0a75\transformed\jetified-datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\915dd48fefe559c6add86ee443dc250d\transformed\jetified-datastore-preferences-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\915dd48fefe559c6add86ee443dc250d\transformed\jetified-datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2aaeb78ad5fd2a6cc00227c76fd465bf\transformed\jetified-datastore-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2aaeb78ad5fd2a6cc00227c76fd465bf\transformed\jetified-datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a0cd823f6dc071c7074eca31ae3b86d\transformed\jetified-firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a0cd823f6dc071c7074eca31ae3b86d\transformed\jetified-firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c076b8a5083df471ddf0ea4b6546222c\transformed\jetified-play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c076b8a5083df471ddf0ea4b6546222c\transformed\jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b95c9d411e3db06edaeaa3bc1f6161\transformed\jetified-firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42b95c9d411e3db06edaeaa3bc1f6161\transformed\jetified-firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5813dff2fc04f128ab917c6cfbab969f\transformed\jetified-play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5813dff2fc04f128ab917c6cfbab969f\transformed\jetified-play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7be1c0bb4075e2006ba23d0329c62fdb\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7be1c0bb4075e2006ba23d0329c62fdb\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\640fc94d5e11f299ffe0019b1ad17aa3\transformed\jetified-appcompat-resources-1.1.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\640fc94d5e11f299ffe0019b1ad17aa3\transformed\jetified-appcompat-resources-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d39894fa66740060eb1456bbd1bb592a\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d39894fa66740060eb1456bbd1bb592a\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46be280a5daaf9ae4cb2602a644b6ed7\transformed\coordinatorlayout-1.0.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46be280a5daaf9ae4cb2602a644b6ed7\transformed\coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34feee06b8e6a00411fec246d529fa0e\transformed\transition-1.4.1\jars\classes.jar"
      resolved="androidx.transition:transition:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34feee06b8e6a00411fec246d529fa0e\transformed\transition-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b956428278ee08b21c0a4142ad7708d\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b956428278ee08b21c0a4142ad7708d\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3072a78261caee9ff1e406e121082d2d\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3072a78261caee9ff1e406e121082d2d\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68ee69209bb66d4bd68b9c5f0b5682df\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68ee69209bb66d4bd68b9c5f0b5682df\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26c2a3dba5cb8c6fbcf32a30713583c0\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26c2a3dba5cb8c6fbcf32a30713583c0\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2778a40c582808460f914c40b7b4e27e\transformed\localbroadcastmanager-1.1.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2778a40c582808460f914c40b7b4e27e\transformed\localbroadcastmanager-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4bcf46c11ecdc3a5924aae0f3248958\transformed\exifinterface-1.3.7\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4bcf46c11ecdc3a5924aae0f3248958\transformed\exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7698ec398f68b5be5ed23deac383e944\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d932d1aee949eca66342e50850feb87f\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d932d1aee949eca66342e50850feb87f\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a70c8b29e3d5c8c14512a87b4434b95\transformed\jetified-firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a70c8b29e3d5c8c14512a87b4434b95\transformed\jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e77c215557459ae56bf1810032d8a567\transformed\jetified-firebase-datatransport-18.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e77c215557459ae56bf1810032d8a567\transformed\jetified-firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2991070276e78974c29daf5c7455c9b4\transformed\jetified-transport-backend-cct-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2991070276e78974c29daf5c7455c9b4\transformed\jetified-transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5adfb241f48e41a0f30f181b9206412f\transformed\jetified-firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5adfb241f48e41a0f30f181b9206412f\transformed\jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0af0d6a33b939b4edcb4d7de9e1fdd30\transformed\jetified-transport-runtime-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0af0d6a33b939b4edcb4d7de9e1fdd30\transformed\jetified-transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56b15de33ae4b371363c376e08513244\transformed\jetified-transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56b15de33ae4b371363c376e08513244\transformed\jetified-transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a01564e41ff22743af50d4dcb4f82591\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a01564e41ff22743af50d4dcb4f82591\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c72c5f9b688d507186600dcf9aab6f\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c72c5f9b688d507186600dcf9aab6f\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61e7ab8b7da5bfa8a35f3f93bc85c658\transformed\jetified-core-1.0.0\jars\classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61e7ab8b7da5bfa8a35f3f93bc85c658\transformed\jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4d0cc0f28414e45927c8d937209f95c\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4d0cc0f28414e45927c8d937209f95c\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb9380fe5960d186becda5af590efe11\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb9380fe5960d186becda5af590efe11\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a124a58d4e90f9cea73b58d9d14c1f57\transformed\jetified-googleid-1.1.0\jars\classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a124a58d4e90f9cea73b58d9d14c1f57\transformed\jetified-googleid-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-okhttp:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-okhttp\1.62.2\2d8b802e7fe17c1577527195fd53a2e7355b3541\grpc-okhttp-1.62.2.jar"
      resolved="io.grpc:grpc-okhttp:1.62.2"/>
  <library
      name="com.squareup.okhttp3:okhttp:3.12.13@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\3.12.13\528e024f340da35cca5d679b631da85d8db9ed\okhttp-3.12.13.jar"
      resolved="com.squareup.okhttp3:okhttp:3.12.13"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.4.0\4e8bd78a52ab935ce383d0092646922154295e54\okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.22\de4a21d6560cadd035c69ba3af3ad1afecc95299\kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.22\ee3bc0c3b55cb516ac92d6a093e1b939166b86a2\kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="org.reactivestreams:reactive-streams:1.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.reactivestreams\reactive-streams\1.0.4\3864a1320d97d7b045f729a326e1e077661f31b7\reactive-streams-1.0.4.jar"
      resolved="org.reactivestreams:reactive-streams:1.0.4"/>
  <library
      name="io.grpc:grpc-android:1.62.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05a63530c5127dd2245c27dc55e87716\transformed\jetified-grpc-android-1.62.2\jars\classes.jar"
      resolved="io.grpc:grpc-android:1.62.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05a63530c5127dd2245c27dc55e87716\transformed\jetified-grpc-android-1.62.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-util:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-util\1.62.2\98c4138f09fb57c3ad6cbeffb31ed73e302038f7\grpc-util-1.62.2.jar"
      resolved="io.grpc:grpc-util:1.62.2"/>
  <library
      name="io.grpc:grpc-core:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-core\1.62.2\5808049a5e33eba6f248a68d58e75399a68f2784\grpc-core-1.62.2.jar"
      resolved="io.grpc:grpc-core:1.62.2"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="com.github.mhiew:pdfium-android:1.9.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa2e0969f82f6816511e19546fbebbfa\transformed\jetified-pdfium-android-1.9.2\jars\classes.jar"
      resolved="com.github.mhiew:pdfium-android:1.9.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa2e0969f82f6816511e19546fbebbfa\transformed\jetified-pdfium-android-1.9.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-protobuf-lite\1.62.2\9d807d2a0e34bd7284a5336186f57cf241090920\grpc-protobuf-lite-1.62.2.jar"
      resolved="io.grpc:grpc-protobuf-lite:1.62.2"/>
  <library
      name="io.grpc:grpc-stub:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-stub\1.62.2\fc1e85697502d96d6c912e8dd2a56f46f1aba050\grpc-stub-1.62.2.jar"
      resolved="io.grpc:grpc-stub:1.62.2"/>
  <library
      name="io.grpc:grpc-context:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-context\1.62.2\69e86c35140b3b1718d65635bb54ccecc4c12f14\grpc-context-1.62.2.jar"
      resolved="io.grpc:grpc-context:1.62.2"/>
  <library
      name="io.grpc:grpc-api:1.62.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-api\1.62.2\a93b6ee3761d48edd9a9279f20a58be1a245ad01\grpc-api-1.62.2.jar"
      resolved="io.grpc:grpc-api:1.62.2"/>
  <library
      name="com.google.guava:guava:32.1.3-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\32.1.3-android\ea090dd85ca2fa12d42d054369df888665230dd7\guava-32.1.3-android.jar"
      resolved="com.google.guava:guava:32.1.3-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="com.google.firebase:protolite-well-known-types:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70857f6080df9dd4d542f83143589e8a\transformed\jetified-protolite-well-known-types-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:protolite-well-known-types:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70857f6080df9dd4d542f83143589e8a\transformed\jetified-protolite-well-known-types-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.26.0\c513866fd91bb46587500440a80fa943e95d12d9\error_prone_annotations-2.26.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.26.0"/>
  <library
      name="com.google.android.play:core-common:2.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19501761fecbdb52c8e982479daffff6\transformed\jetified-core-common-2.0.3\jars\classes.jar"
      resolved="com.google.android.play:core-common:2.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19501761fecbdb52c8e982479daffff6\transformed\jetified-core-common-2.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.protobuf:protobuf-javalite:3.25.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.protobuf\protobuf-javalite\3.25.5\272641fe157ed7c4a22f8d4c347bcd7f6eac8887\protobuf-javalite-3.25.5.jar"
      resolved="com.google.protobuf:protobuf-javalite:3.25.5"/>
  <library
      name="io.perfmark:perfmark-api:0.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.perfmark\perfmark-api\0.26.0\ef65452adaf20bf7d12ef55913aba24037b82738\perfmark-api-0.26.0.jar"
      resolved="io.perfmark:perfmark-api:0.26.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.android:annotations:*******@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.android\annotations\*******\a1678ba907bf92691d879fef34e1a187038f9259\annotations-*******.jar"
      resolved="com.google.android:annotations:*******"/>
  <library
      name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.codehaus.mojo\animal-sniffer-annotations\1.23\3c0daebd5f0e1ce72cc50c818321ac957aeb5d70\animal-sniffer-annotations-1.23.jar"
      resolved="org.codehaus.mojo:animal-sniffer-annotations:1.23"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="org.checkerframework:checker-qual:3.37.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.37.0\ba74746d38026581c12166e164bb3c15e90cc4ea\checker-qual-3.37.0.jar"
      resolved="org.checkerframework:checker-qual:3.37.0"/>
</libraries>
